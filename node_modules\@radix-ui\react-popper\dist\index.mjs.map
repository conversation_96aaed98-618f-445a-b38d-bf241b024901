{"version": 3, "sources": ["../src/popper.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    const anchorRef = React.useRef<Measurable | null>(null);\n    React.useEffect(() => {\n      const previousAnchor = anchorRef.current;\n      anchorRef.current = virtualRef?.current || ref.current;\n      if (previousAnchor !== anchorRef.current) {\n        // Consumer can anchor the popper to something that isn't\n        // a DOM node e.g. pointer position, so we override the\n        // `anchorRef` with their virtual ref in this case.\n        context.onAnchorChange(anchorRef.current);\n      }\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ComponentRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ComponentRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n"], "mappings": ";;;AAAA,YAAY,WAAW;AACvB;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS;AAAA,EACT;AAAA,EACA;AAAA,OACK;AACP,YAAY,oBAAoB;AAChC,SAAS,uBAAuB;AAChC,SAAS,0BAA0B;AACnC,SAAS,iBAAiB;AAC1B,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,eAAe;AAkCpB;AA5BJ,IAAM,eAAe,CAAC,OAAO,SAAS,UAAU,MAAM;AACtD,IAAM,gBAAgB,CAAC,SAAS,UAAU,KAAK;AAS/C,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAM/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAK9F,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM,EAAE,eAAe,SAAS,IAAI;AACpC,QAAM,CAAC,QAAQ,SAAS,IAAU,eAA4B,IAAI;AAClE,SACE,oBAAC,kBAAe,OAAO,eAAe,QAAgB,gBAAgB,WACnE,UACH;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,cAAc;AAQpB,IAAM,eAAqB;AAAA,EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,eAAe,YAAY,GAAG,YAAY,IAAI;AACtD,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,UAAM,MAAY,aAA4B,IAAI;AAClD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAEtD,UAAM,YAAkB,aAA0B,IAAI;AACtD,IAAM,gBAAU,MAAM;AACpB,YAAM,iBAAiB,UAAU;AACjC,gBAAU,UAAU,YAAY,WAAW,IAAI;AAC/C,UAAI,mBAAmB,UAAU,SAAS;AAIxC,gBAAQ,eAAe,UAAU,OAAO;AAAA,MAC1C;AAAA,IACF,CAAC;AAED,WAAO,aAAa,OAAO,oBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,cAAc;AAAA,EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAUrB,IAAM,CAAC,uBAAuB,iBAAiB,IAC7C,oBAA+C,YAAY;AAoB7D,IAAM,gBAAsB;AAAA,EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM;AAAA,MACJ;AAAA,MACA,OAAO;AAAA,MACP,aAAa;AAAA,MACb,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,oBAAoB,CAAC;AAAA,MACrB,kBAAkB,uBAAuB;AAAA,MACzC,SAAS;AAAA,MACT,mBAAmB;AAAA,MACnB,yBAAyB;AAAA,MACzB;AAAA,MACA,GAAG;AAAA,IACL,IAAI;AAEJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAE5D,UAAM,CAAC,SAAS,UAAU,IAAU,eAAgC,IAAI;AACxE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAE7E,UAAM,CAAC,OAAO,QAAQ,IAAU,eAAiC,IAAI;AACrE,UAAM,YAAY,QAAQ,KAAK;AAC/B,UAAM,aAAa,WAAW,SAAS;AACvC,UAAM,cAAc,WAAW,UAAU;AAEzC,UAAM,mBAAoB,QAAQ,UAAU,WAAW,MAAM,QAAQ;AAErE,UAAM,mBACJ,OAAO,yBAAyB,WAC5B,uBACA,EAAE,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,GAAG,qBAAqB;AAEtE,UAAM,WAAW,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB,CAAC,iBAAiB;AAC1F,UAAM,wBAAwB,SAAS,SAAS;AAEhD,UAAM,wBAAwB;AAAA,MAC5B,SAAS;AAAA,MACT,UAAU,SAAS,OAAO,SAAS;AAAA;AAAA,MAEnC,aAAa;AAAA,IACf;AAEA,UAAM,EAAE,MAAM,gBAAgB,WAAW,cAAc,eAAe,IAAI,YAAY;AAAA;AAAA,MAEpF,UAAU;AAAA,MACV,WAAW;AAAA,MACX,sBAAsB,IAAI,SAAS;AACjC,cAAM,UAAU,WAAW,GAAG,MAAM;AAAA,UAClC,gBAAgB,2BAA2B;AAAA,QAC7C,CAAC;AACD,eAAO;AAAA,MACT;AAAA,MACA,UAAU;AAAA,QACR,WAAW,QAAQ;AAAA,MACrB;AAAA,MACA,YAAY;AAAA,QACV,OAAO,EAAE,UAAU,aAAa,aAAa,eAAe,YAAY,CAAC;AAAA,QACzE,mBACE,MAAM;AAAA,UACJ,UAAU;AAAA,UACV,WAAW;AAAA,UACX,SAAS,WAAW,YAAY,WAAW,IAAI;AAAA,UAC/C,GAAG;AAAA,QACL,CAAC;AAAA,QACH,mBAAmB,KAAK,EAAE,GAAG,sBAAsB,CAAC;AAAA,QACpD,KAAK;AAAA,UACH,GAAG;AAAA,UACH,OAAO,CAAC,EAAE,UAAU,OAAO,gBAAgB,gBAAgB,MAAM;AAC/D,kBAAM,EAAE,OAAO,aAAa,QAAQ,aAAa,IAAI,MAAM;AAC3D,kBAAM,eAAe,SAAS,SAAS;AACvC,yBAAa,YAAY,kCAAkC,GAAG,cAAc,IAAI;AAChF,yBAAa,YAAY,mCAAmC,GAAG,eAAe,IAAI;AAClF,yBAAa,YAAY,+BAA+B,GAAG,WAAW,IAAI;AAC1E,yBAAa,YAAY,gCAAgC,GAAG,YAAY,IAAI;AAAA,UAC9E;AAAA,QACF,CAAC;AAAA,QACD,SAAS,gBAAgB,EAAE,SAAS,OAAO,SAAS,aAAa,CAAC;AAAA,QAClE,gBAAgB,EAAE,YAAY,YAAY,CAAC;AAAA,QAC3C,oBAAoB,KAAK,EAAE,UAAU,mBAAmB,GAAG,sBAAsB,CAAC;AAAA,MACpF;AAAA,IACF,CAAC;AAED,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AAExE,UAAM,eAAe,eAAe,QAAQ;AAC5C,oBAAgB,MAAM;AACpB,UAAI,cAAc;AAChB,uBAAe;AAAA,MACjB;AAAA,IACF,GAAG,CAAC,cAAc,YAAY,CAAC;AAE/B,UAAM,SAAS,eAAe,OAAO;AACrC,UAAM,SAAS,eAAe,OAAO;AACrC,UAAM,oBAAoB,eAAe,OAAO,iBAAiB;AAEjE,UAAM,CAAC,eAAe,gBAAgB,IAAU,eAAiB;AACjE,oBAAgB,MAAM;AACpB,UAAI,QAAS,kBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;AAAA,IACvE,GAAG,CAAC,OAAO,CAAC;AAEZ,WACE;AAAA,MAAC;AAAA;AAAA,QACC,KAAK,KAAK;AAAA,QACV,qCAAkC;AAAA,QAClC,OAAO;AAAA,UACL,GAAG;AAAA,UACH,WAAW,eAAe,eAAe,YAAY;AAAA;AAAA,UACrD,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,CAAC,iCAAwC,GAAG;AAAA,YAC1C,eAAe,iBAAiB;AAAA,YAChC,eAAe,iBAAiB;AAAA,UAClC,EAAE,KAAK,GAAG;AAAA;AAAA;AAAA;AAAA,UAKV,GAAI,eAAe,MAAM,mBAAmB;AAAA,YAC1C,YAAY;AAAA,YACZ,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,QAIA,KAAK,MAAM;AAAA,QAEX;AAAA,UAAC;AAAA;AAAA,YACC,OAAO;AAAA,YACP;AAAA,YACA,eAAe;AAAA,YACf;AAAA,YACA;AAAA,YACA,iBAAiB;AAAA,YAEjB;AAAA,cAAC,UAAU;AAAA,cAAV;AAAA,gBACC,aAAW;AAAA,gBACX,cAAY;AAAA,gBACX,GAAG;AAAA,gBACJ,KAAK;AAAA,gBACL,OAAO;AAAA,kBACL,GAAG,aAAa;AAAA;AAAA;AAAA,kBAGhB,WAAW,CAAC,eAAe,SAAS;AAAA,gBACtC;AAAA;AAAA,YACF;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAEnB,IAAM,gBAAoC;AAAA,EACxC,KAAK;AAAA,EACL,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,MAAM;AACR;AAMA,IAAM,cAAoB,iBAAiD,SAASA,aAClF,OACA,cACA;AACA,QAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,QAAM,iBAAiB,kBAAkB,YAAY,aAAa;AAClE,QAAM,WAAW,cAAc,eAAe,UAAU;AAExD;AAAA;AAAA;AAAA;AAAA,IAIE;AAAA,MAAC;AAAA;AAAA,QACC,KAAK,eAAe;AAAA,QACpB,OAAO;AAAA,UACL,UAAU;AAAA,UACV,MAAM,eAAe;AAAA,UACrB,KAAK,eAAe;AAAA,UACpB,CAAC,QAAQ,GAAG;AAAA,UACZ,iBAAiB;AAAA,YACf,KAAK;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,MAAM;AAAA,UACR,EAAE,eAAe,UAAU;AAAA,UAC3B,WAAW;AAAA,YACT,KAAK;AAAA,YACL,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,MAAM;AAAA,UACR,EAAE,eAAe,UAAU;AAAA,UAC3B,YAAY,eAAe,kBAAkB,WAAW;AAAA,QAC1D;AAAA,QAEA;AAAA,UAAgB;AAAA,UAAf;AAAA,YACE,GAAG;AAAA,YACJ,KAAK;AAAA,YACL,OAAO;AAAA,cACL,GAAG,WAAW;AAAA;AAAA,cAEd,SAAS;AAAA,YACX;AAAA;AAAA,QACF;AAAA;AAAA,IACF;AAAA;AAEJ,CAAC;AAED,YAAY,cAAc;AAI1B,SAAS,UAAa,OAA6B;AACjD,SAAO,UAAU;AACnB;AAEA,IAAM,kBAAkB,CAAC,aAAsE;AAAA,EAC7F,MAAM;AAAA,EACN;AAAA,EACA,GAAG,MAAM;AACP,UAAM,EAAE,WAAW,OAAO,eAAe,IAAI;AAE7C,UAAM,oBAAoB,eAAe,OAAO,iBAAiB;AACjE,UAAM,gBAAgB;AACtB,UAAM,aAAa,gBAAgB,IAAI,QAAQ;AAC/C,UAAM,cAAc,gBAAgB,IAAI,QAAQ;AAEhD,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AACxE,UAAM,eAAe,EAAE,OAAO,MAAM,QAAQ,OAAO,KAAK,OAAO,EAAE,WAAW;AAE5E,UAAM,gBAAgB,eAAe,OAAO,KAAK,KAAK,aAAa;AACnE,UAAM,gBAAgB,eAAe,OAAO,KAAK,KAAK,cAAc;AAEpE,QAAI,IAAI;AACR,QAAI,IAAI;AAER,QAAI,eAAe,UAAU;AAC3B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,CAAC,WAAW;AAAA,IACrB,WAAW,eAAe,OAAO;AAC/B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,MAAM,SAAS,SAAS,WAAW;AAAA,IAC5C,WAAW,eAAe,SAAS;AACjC,UAAI,GAAG,CAAC,WAAW;AACnB,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAAA,IACpD,WAAW,eAAe,QAAQ;AAChC,UAAI,GAAG,MAAM,SAAS,QAAQ,WAAW;AACzC,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAAA,IACpD;AACA,WAAO,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;AAAA,EAC1B;AACF;AAEA,SAAS,6BAA6B,WAAsB;AAC1D,QAAM,CAAC,MAAM,QAAQ,QAAQ,IAAI,UAAU,MAAM,GAAG;AACpD,SAAO,CAAC,MAAc,KAAc;AACtC;AAEA,IAAMC,QAAO;AACb,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,QAAQ;", "names": ["PopperArrow", "Root"]}